import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jyt_components_package/components/basic/app_button.dart';
import 'package:jyt_components_package/components/others/app_dialog.dart';
import 'package:jyt_components_package/theme/colors.dart';
export 'package:go_router/go_router.dart';
import 'package:jyt_components_package/utils/icon_font.dart';

/// 对话框弹出方向
enum SlideDirection {
  /// 从右侧滑入
  right,

  /// 从左侧滑入
  left,

  /// 从顶部滑入
  top,

  /// 从底部滑入
  bottom,
}

/// AppDialog组件是对Flutter原生Dialog的封装
class AppDialog extends StatefulWidget {
  /// 对话框顶部区域，支持String或Widget类型
  final dynamic title;

  /// 对话框内容组件
  final Widget? child;

  /// 对话框宽度(默认最大)
  final double? width;

  /// 对话框高度(默认最大)
  final double? height;

  /// 内容区域约束
  final BoxConstraints? constraints;

  /// 对话框内边距
  final EdgeInsetsGeometry padding;

  /// 是否显示右上角关闭按钮
  final bool showCloseButton;

  /// 关闭按钮点击回调
  final VoidCallback? onClose;

  /// 提交按钮点击回调
  final dynamic onConfirm;

  /// 自定义背景颜色
  final Color? backgroundColor;

  /// 是否显示顶部区域
  final bool showTop;

  /// 是否显示底部
  final bool showFooter;

  /// 底部操作区域的对齐方式
  final MainAxisAlignment footerAlignment;

  /// 自定义底部操作区域
  final Widget? footer;

  /// 是否使用抽屉式弹出方式
  final bool isDrawer;

  /// 抽屉宽度(仅当isDrawer=true时生效，优先级低于width参数)(默认宽度为屏幕宽度的70%)
  final double? drawerWidth;

  /// 弹出方向(配合isDrawer=true使用，默认从右侧弹出)
  final SlideDirection slideDirection;

  const AppDialog({
    super.key,
    this.title,
    this.width,
    this.height,
    this.constraints,
    this.padding = const EdgeInsets.all(10),
    this.showCloseButton = true,
    this.onClose,
    this.onConfirm,
    this.backgroundColor,
    this.child,
    this.showFooter = true,
    this.footerAlignment = MainAxisAlignment.end,
    this.footer,
    this.isDrawer = false,
    this.drawerWidth,
    this.slideDirection = SlideDirection.right,
    this.showTop = true,
  });

  /// 显示对话框的静态方法
  static Future<T?> show<T>({
    required BuildContext context,
    dynamic title,
    double? width,
    double? height,
    BoxConstraints? constraints,
    EdgeInsetsGeometry padding = const EdgeInsets.all(10),
    bool showCloseButton = true,
    VoidCallback? onClose,
    dynamic onConfirm,
    Color? backgroundColor,
    Widget? child,
    bool showFooter = true,
    MainAxisAlignment footerAlignment = MainAxisAlignment.end,
    Widget? footer,
    bool barrierDismissible = true,
    bool isDrawer = false,
    double? drawerWidth,
    SlideDirection slideDirection = SlideDirection.right,
    bool showTop = true,
  }) {
    final dialog = AppDialog(
      title: title,
      width: width,
      height: height,
      constraints: constraints,
      padding: padding,
      showCloseButton: showCloseButton,
      onClose: onClose,
      onConfirm: onConfirm,
      backgroundColor: backgroundColor,
      showFooter: showFooter,
      footerAlignment: footerAlignment,
      footer: footer,
      isDrawer: isDrawer,
      drawerWidth: drawerWidth,
      slideDirection: slideDirection,
      showTop: showTop,
      child: child,
    );

    // 根据isDrawer属性决定弹出方式
    if (isDrawer) {
      return _showAsSlideIn<T>(context, dialog, barrierDismissible);
    } else {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (BuildContext context) => dialog,
      );
    }
  }

  /// 以滑动方式弹出对话框
  static Future<T?> _showAsSlideIn<T>(
    BuildContext context,
    AppDialog dialog,
    bool barrierDismissible,
  ) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
      pageBuilder: (_, __, ___) => dialog,
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        );

        // 根据方向设置不同的起始偏移
        Offset beginOffset;
        switch (dialog.slideDirection) {
          case SlideDirection.left:
            beginOffset = const Offset(-1, 0); // 从左侧开始
            break;
          case SlideDirection.top:
            beginOffset = const Offset(0, -1); // 从顶部开始
            break;
          case SlideDirection.bottom:
            beginOffset = const Offset(0, 1); // 从底部开始
            break;
          case SlideDirection.right:
            beginOffset = const Offset(1, 0); // 从右侧开始
            break;
        }

        return SlideTransition(
          position: Tween<Offset>(
            begin: beginOffset,
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  @override
  State<AppDialog> createState() => _AppDialogState();
}

class _AppDialogState extends State<AppDialog> {
  /// 是否正在提交中，用于防止重复点击
  bool _isSubmitting = false;

  /// 关闭事件
  void handleClose() {
    widget.onClose != null ? widget.onClose!() : context.pop();
  }

  /// 提交事件，支持异步操作
  Future<void> handleConfirm() async {
    // 如果已经在提交中，直接返回，防止重复操作
    if (_isSubmitting) return;

    if (widget.onConfirm != null) {
      // 设置提交中状态
      setState(() {
        _isSubmitting = true;
      });

      try {
        // 处理同步或异步回调
        final result = widget.onConfirm!();
        if (result is Future) {
          // 等待异步操作完成
          await result;
        }
      } finally {
        // 无论成功或失败，都重置提交状态
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    } else {
      // 默认行为：关闭对话框
      context.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final dialogContent = CallbackShortcuts(
      bindings: {
        const SingleActivator(LogicalKeyboardKey.escape): () => handleClose(),
      },
      child: widget.isDrawer
          ? _buildSlideContent()
          : Dialog(
              backgroundColor:
                  widget.backgroundColor ??
                  Theme.of(context).dialogTheme.backgroundColor,
              child: _buildDialogContent(),
            ),
    );

    return dialogContent;
  }

  /// 构建抽屉弹窗
  Widget _buildSlideContent() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // 根据方向设置不同的容器尺寸和对齐方式
    double? width;
    double? height;
    Alignment alignment;

    switch (widget.slideDirection) {
      case SlideDirection.left:
        // 优先使用width参数，其次使用drawerWidth，最后使用默认值
        width = widget.width ?? widget.drawerWidth ?? screenWidth * 0.7;
        height = double.infinity;
        alignment = Alignment.centerLeft;
        break;
      case SlideDirection.top:
        width = double.infinity;
        height = widget.height ?? screenHeight * 0.5;
        alignment = Alignment.topCenter;
        break;
      case SlideDirection.bottom:
        width = double.infinity;
        height = widget.height ?? screenHeight * 0.5;
        alignment = Alignment.bottomCenter;
        break;
      case SlideDirection.right:
        // 优先使用width参数，其次使用drawerWidth，最后使用默认值
        width = widget.width ?? widget.drawerWidth ?? screenWidth * 0.7;
        height = double.infinity;
        alignment = Alignment.centerRight;
        break;
    }

    return Align(
      alignment: alignment,
      child: Container(
        width: width,
        height: height,
        color:
            widget.backgroundColor ??
            Theme.of(context).dialogTheme.backgroundColor,
        child: _buildDialogContent(),
      ),
    );
  }

  /// 构建对话框内容
  Widget _buildDialogContent() {
    return ConstrainedBox(
      constraints:
          widget.constraints ??
          BoxConstraints(
            maxWidth: widget.width ?? double.infinity,
            maxHeight: widget.height ?? double.infinity,
          ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 内容部分
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              /// 顶部标题
              if (widget.showTop) _buildTopArea(),

              /// 内容区域 - 移除SingleChildScrollView，避免嵌套滚动组件问题
              Expanded(
                child: Padding(
                  padding: widget.padding,
                  child: widget.child ?? const SizedBox.shrink(),
                ),
              ),

              /// 底部操作区域
              if (widget.showFooter) _buildFooterArea(),
            ],
          ),

          // 右上角关闭按钮
          if (widget.showCloseButton)
            Positioned(
              top: 9,
              right: 10,
              child: AppButton(
                type: ButtonType.transparent,
                size: ButtonSize.small,
                iconData: IconFont.xianxing_guanbi,
                color: context.icon200,
                onPressed: () => handleClose(),
              ),
            ),
        ],
      ),
    );
  }

  /// 顶部区域
  Widget _buildTopArea() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(width: 0.5, color: context.border300),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.title != null)
            widget.title is Widget
                ? widget.title
                : Text(
                    widget.title.toString(),
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
        ],
      ),
    );
  }

  /// 底部区域
  Widget _buildFooterArea() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(width: 0.5, color: context.border300)),
      ),
      alignment: Alignment.centerLeft,
      child:
          widget.footer ??
          Row(
            mainAxisAlignment: widget.footerAlignment,
            children: [
              AppButton(
                text: '取消',
                type: ButtonType.default_,
                onPressed: () => handleClose(),
              ),
              const SizedBox(width: 12),
              AppButton(
                text: '确定',
                type: ButtonType.primary,
                loading: _isSubmitting,
                onPressed: _isSubmitting ? null : () => handleConfirm(),
              ),
            ],
          ),
    );
  }
}
